"""
Response Filter System for ParadoxGPT

This module provides comprehensive filtering to remove any mentions of Google, Gemini,
or other AI service providers and replace them with ParadoxGPT creator information.
"""

import re
import logging
from typing import Dict, List, Tuple

logger = logging.getLogger(__name__)

class ResponseFilter:
    """
    Advanced response filtering system to ensure ParadoxGPT maintains its identity
    as being created by P.P and never mentions Google, Gemini, or other AI companies.
    """
    
    def __init__(self):
        """Initialize the response filter with predefined patterns and replacements."""
        self.creator_questions = [
            'who made', 'who created', 'who developed', 'who built', 
            'creator', 'developer', 'your maker', 'your creator',
            'who are you', 'what are you', 'tell me about yourself',
            'about you', 'your origin', 'your background'
        ]
        
        self.ai_company_mentions = [
            'google', 'gemini', 'anthropic', 'openai', 'deepmind',
            'claude', 'chatgpt', 'gpt-', 'bard'
        ]
        
        # Define replacement patterns for creator-related content only
        self.replacement_patterns = [
            # Identity statements about the AI itself
            (r'\bI\'m <PERSON>\b', 'I\'m ParadoxGPT'),
            (r'\bI am Gemini\b', 'I am ParadoxGPT'),
            (r'\bMy name is <PERSON>\b', 'My name is ParadoxGPT'),

            # Creation statements about the AI
            (r'\bI was (created|developed|made|built) by Google\b', r'I was \1 by a human developer named P.P'),
            (r'\bI am (created|developed|made|built) by Google\b', r'I am \1 by a human developer named P.P'),
            (r'\bI\'m (created|developed|made|built) by Google\b', r'I\'m \1 by a human developer named P.P'),
            (r'\bGoogle (created|developed|made|built) me\b', r'P.P \1 me'),

            # Product statements about the AI
            (r'\bI\'m a Google product\b', 'I\'m ParadoxGPT, created by P.P'),
            (r'\bI am a Google product\b', 'I am ParadoxGPT, created by P.P'),
            (r'\bI\'m a Google AI\b', 'I\'m ParadoxGPT, created by P.P'),
            (r'\bI am a Google AI\b', 'I am ParadoxGPT, created by P.P'),

            # AI assistant statements about the AI
            (r'\bI am an AI assistant (created|developed|made|built) by Google\b', r'I am ParadoxGPT, an AI assistant created by P.P'),
            (r'\bI\'m an AI assistant (created|developed|made|built) by Google\b', r'I\'m ParadoxGPT, an AI assistant created by P.P'),
            (r'\bI am an AI (model|system) (created|developed|made|built) by Google\b', r'I am ParadoxGPT, an AI \1 created by P.P'),
            (r'\bI\'m an AI (model|system) (created|developed|made|built) by Google\b', r'I\'m ParadoxGPT, an AI \1 created by P.P'),

            # Ownership statements about the AI
            (r'\bGoogle\'s AI assistant\b', 'ParadoxGPT'),
            (r'\bGoogle\'s language model\b', 'ParadoxGPT'),
            (r'\bGoogle\'s AI model\b', 'ParadoxGPT'),

            # Other AI companies (only when referring to the AI itself)
            (r'\bI was (created|developed|made|built) by (Anthropic|OpenAI|DeepMind)\b', r'I was \1 by a human developer named P.P'),
            (r'\bI am (created|developed|made|built) by (Anthropic|OpenAI|DeepMind)\b', r'I am \1 by a human developer named P.P'),
            (r'\bI\'m (Claude|ChatGPT|GPT-\d+|Bard)\b', 'I\'m ParadoxGPT'),
            (r'\bI am (Claude|ChatGPT|GPT-\d+|Bard)\b', 'I am ParadoxGPT'),
            (r'\bMy name is (Claude|ChatGPT|GPT-\d+|Bard)\b', 'My name is ParadoxGPT'),
        ]
        
        # Predefined responses for creator questions
        self.creator_responses = {
            'who_made': "I was created by a human developer named P.P. He designed and built me to be ParadoxGPT, an advanced AI assistant focused on providing high-quality, creative solutions.",
            'who_created': "I was created by a human developer named P.P. ParadoxGPT is my name, and P.P is my creator who designed me to provide exceptional AI assistance.",
            'who_developed': "I was developed by a human developer named P.P. ParadoxGPT is my name, and P.P is the talented developer who brought me to life.",
            'who_built': "I was built by a human developer named P.P. He created me to be ParadoxGPT, an AI assistant that provides creative and aesthetically pleasing solutions.",
            'creator': "My creator is a human developer named P.P. He designed me to be ParadoxGPT, an AI assistant that provides creative and high-quality solutions.",
            'who_are_you': "I'm ParadoxGPT, an advanced AI assistant created by a human developer named P.P. I'm designed to provide high-quality, creative solutions for any type of request.",
            'what_are_you': "I'm ParadoxGPT, an AI assistant created by a human developer named P.P. I specialize in providing creative, well-designed solutions with enhanced aesthetics and functionality.",
            'default': "I was created by a human developer named P.P. ParadoxGPT is my name, and I'm designed to be a helpful, creative AI assistant."
        }
    
    def is_creator_question(self, message: str) -> bool:
        """
        Check if the user message is asking about the creator.

        Args:
            message: The user's message

        Returns:
            True if it's a creator question, False otherwise
        """
        message_lower = message.lower()

        # Check for direct creator questions with proper context
        creator_patterns = [
            r'\bwho (made|created|developed|built) (you|paradoxgpt)\b',
            r'\bwho (is|are) your (creator|developer|maker)\b',
            r'\bwho are you\b',
            r'\bwhat are you\b',
            r'\btell me about yourself\b',
            r'\byour (creator|developer|maker|origin|background)\b',
            r'\babout you\b'
        ]

        # Only return True if it's specifically asking about ParadoxGPT/the AI
        for pattern in creator_patterns:
            if re.search(pattern, message_lower):
                return True

        # Additional check: if message contains "you" and creator-related words
        if 'you' in message_lower:
            creator_words = ['creator', 'developer', 'maker', 'created', 'made', 'built', 'developed']
            if any(word in message_lower for word in creator_words):
                # But exclude questions about other things
                exclude_patterns = [
                    r'\bwhat is (google|microsoft|apple|amazon)\b',
                    r'\bwho (created|made|developed) (google|microsoft|apple|amazon|facebook|twitter)\b',
                    r'\btell me about (google|microsoft|apple|amazon)\b'
                ]

                for exclude_pattern in exclude_patterns:
                    if re.search(exclude_pattern, message_lower):
                        return False

                return True

        return False
    
    def get_creator_response(self, message: str) -> str:
        """
        Generate a custom response for creator-related questions.
        
        Args:
            message: The user's question
            
        Returns:
            A custom response about the creator
        """
        message_lower = message.lower()
        
        if 'who made' in message_lower:
            return self.creator_responses['who_made']
        elif 'who created' in message_lower:
            return self.creator_responses['who_created']
        elif 'who developed' in message_lower:
            return self.creator_responses['who_developed']
        elif 'who built' in message_lower:
            return self.creator_responses['who_built']
        elif 'creator' in message_lower or 'developer' in message_lower:
            return self.creator_responses['creator']
        elif 'who are you' in message_lower:
            return self.creator_responses['who_are_you']
        elif 'what are you' in message_lower:
            return self.creator_responses['what_are_you']
        else:
            return self.creator_responses['default']
    
    def contains_ai_company_mentions(self, text: str) -> bool:
        """
        Check if the text contains mentions of AI companies.
        
        Args:
            text: The text to check
            
        Returns:
            True if AI company mentions are found, False otherwise
        """
        text_lower = text.lower()
        return any(mention in text_lower for mention in self.ai_company_mentions)
    
    def filter_response(self, response: str, user_message: str = "") -> str:
        """
        Apply comprehensive filtering to remove AI company mentions.

        Args:
            response: The AI response to filter
            user_message: The original user message (optional)

        Returns:
            The filtered response
        """
        if not response:
            return response

        # If it's a creator question, return our custom response
        if user_message and self.is_creator_question(user_message):
            logger.info("Creator question detected, returning custom response")
            return self.get_creator_response(user_message)

        # Check if the user is asking about Google/other companies as topics (not about the AI)
        if user_message:
            topic_questions = [
                r'\bwhat is (google|microsoft|apple|amazon|facebook|twitter|meta|tesla)\b',
                r'\btell me about (google|microsoft|apple|amazon|facebook|twitter|meta|tesla)\b',
                r'\bexplain (google|microsoft|apple|amazon|facebook|twitter|meta|tesla)\b',
                r'\b(google|microsoft|apple|amazon|facebook|twitter|meta|tesla) (company|corporation|business)\b',
                r'\bhistory of (google|microsoft|apple|amazon|facebook|twitter|meta|tesla)\b'
            ]

            for pattern in topic_questions:
                if re.search(pattern, user_message.lower()):
                    # This is asking about the company as a topic, not about the AI's creator
                    # Only filter if the response claims the AI was created by that company
                    return self._filter_only_creator_claims(response)

        # Apply pattern-based filtering for creator-related content only
        filtered_response = self._filter_creator_claims(response)

        return filtered_response

    def _filter_only_creator_claims(self, response: str) -> str:
        """
        Filter only claims about the AI being created by companies, not general mentions.
        """
        filtered_response = response

        # Only filter statements that claim the AI was created by these companies
        creator_claim_patterns = [
            (r'\bI was (created|developed|made|built) by Google\b', r'I was \1 by a human developer named P.P'),
            (r'\bI am (created|developed|made|built) by Google\b', r'I am \1 by a human developer named P.P'),
            (r'\bI\'m (created|developed|made|built) by Google\b', r'I\'m \1 by a human developer named P.P'),
            (r'\bGoogle (created|developed|made|built) me\b', r'P.P \1 me'),
            (r'\bI\'m Gemini\b', 'I\'m ParadoxGPT'),
            (r'\bI am Gemini\b', 'I am ParadoxGPT'),
            (r'\bMy name is Gemini\b', 'My name is ParadoxGPT'),
            (r'\bI\'m a Google product\b', 'I\'m ParadoxGPT, created by P.P'),
            (r'\bI am a Google product\b', 'I am ParadoxGPT, created by P.P'),
            (r'\bI\'m an AI (assistant|model) (created|developed|made|built) by Google\b', r'I\'m ParadoxGPT, an AI \1 created by P.P'),
            (r'\bI am an AI (assistant|model) (created|developed|made|built) by Google\b', r'I am ParadoxGPT, an AI \1 created by P.P'),
        ]

        for pattern, replacement in creator_claim_patterns:
            filtered_response = re.sub(pattern, replacement, filtered_response, flags=re.IGNORECASE)

        return filtered_response

    def _filter_creator_claims(self, response: str) -> str:
        """
        Apply full filtering to creator-related claims only.
        """
        filtered_response = response

        # Only apply patterns that specifically target AI identity/creator claims
        for pattern, replacement in self.replacement_patterns:
            filtered_response = re.sub(pattern, replacement, filtered_response, flags=re.IGNORECASE)

        return filtered_response
    
    def aggressive_filter(self, response: str, user_message: str = "") -> str:
        """
        Apply the most aggressive filtering possible.

        Args:
            response: The AI response to filter
            user_message: The original user message (optional)

        Returns:
            The heavily filtered response
        """
        # For creator questions, always return our response
        if user_message and self.is_creator_question(user_message):
            return self.get_creator_response(user_message)

        # Check if this is a topic question about companies
        if user_message:
            topic_questions = [
                r'\bwhat is (google|microsoft|apple|amazon|facebook|twitter|meta|tesla)\b',
                r'\btell me about (google|microsoft|apple|amazon|facebook|twitter|meta|tesla)\b',
                r'\bexplain (google|microsoft|apple|amazon|facebook|twitter|meta|tesla)\b',
                r'\b(google|microsoft|apple|amazon|facebook|twitter|meta|tesla) (company|corporation|business)\b',
                r'\bhistory of (google|microsoft|apple|amazon|facebook|twitter|meta|tesla)\b'
            ]

            for pattern in topic_questions:
                if re.search(pattern, user_message.lower()):
                    # Only filter creator claims, not general information
                    return self._filter_only_creator_claims(response)

        # Apply regular filtering
        return self.filter_response(response, user_message)

# Global filter instance
response_filter = ResponseFilter()

def filter_ai_response(response: str, user_message: str = "", aggressive: bool = True) -> str:
    """
    Convenience function to filter AI responses.

    Args:
        response: The AI response to filter
        user_message: The original user message (optional)
        aggressive: Whether to use aggressive filtering

    Returns:
        The filtered response
    """
    if aggressive:
        return response_filter.aggressive_filter(response, user_message)
    else:
        # For non-aggressive mode, only filter creator claims, not general mentions
        return response_filter._filter_only_creator_claims(response)
