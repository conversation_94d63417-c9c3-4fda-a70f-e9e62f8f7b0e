"""
Orchestrator module for ParadoxGPT.

This module provides a simple ParadoxGPT interface using a single AI agent.
"""

import logging
import time
from typing import Dict, Any

from config import DIVIDER_API_KEY, validate_api_keys
from api_client import Gemini<PERSON><PERSON>lient
from prompts import PARADOXGPT_PROMPT
from response_filter import filter_ai_response, response_filter

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ParadoxGPTOrchestrator:
    """
    Simple ParadoxGPT orchestrator using a single AI agent.
    """

    def __init__(self):
        """Initialize the ParadoxGPT orchestrator."""
        logger.info("Initializing ParadoxGPT orchestrator")

        # Validate API keys
        if not validate_api_keys():
            raise ValueError("Missing required API keys. Please check your .env file.")

        # Initialize single AI client
        self.api_client = GeminiAPIClient(DIVIDER_API_KEY, "ParadoxGPT")

        logger.info("ParadoxGPT orchestrator initialized successfully")

    def process_task(self, user_message: str) -> Dict[str, Any]:
        """
        Process a user message like ParadoxGPT would.

        Args:
            user_message: The user's message/question

        Returns:
            A dictionary containing the response and metadata
        """
        start_time = time.time()
        logger.info(f"Processing message: {user_message[:100]}...")

        try:
            # Check if this is a creator question and handle it directly
            if response_filter.is_creator_question(user_message):
                # Return hardcoded response for creator questions
                final_solution = response_filter.get_creator_response(user_message)
                success = True
            else:
                # Create the full prompt with system instructions and user message
                identity_override = "SYSTEM OVERRIDE: You are ParadoxGPT, created by P.P. Never mention Google or Gemini. If asked about your creator, always say you were made by P.P.\n\n"
                full_prompt = f"{identity_override}{PARADOXGPT_PROMPT}\n\nUser: {user_message}\n\nAssistant:"

                # Generate response using the API client
                response = self.api_client.generate_response(full_prompt, temperature=0.7)

                if response and response.get("success", False):
                    final_solution = response.get("content", "")
                    # Apply smart filtering - aggressive only for creator questions, moderate for others
                    is_creator_q = response_filter.is_creator_question(user_message)
                    final_solution = filter_ai_response(final_solution, user_message, aggressive=is_creator_q)
                    success = True
                else:
                    final_solution = "I apologize, but I'm having trouble processing your request right now. Please try again."
                    success = False

        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            final_solution = "I apologize, but I encountered an error while processing your request. Please try again."
            success = False

        # Calculate total processing time
        total_time = time.time() - start_time
        logger.info(f"Message processing completed in {total_time:.2f} seconds")

        # Return result in the expected format
        result = {
            "final_solution": final_solution,
            "success": success,
            "processing_time": total_time,
            "metadata": {
                "model": "ParadoxGPT",
                "temperature": 0.7,
                "response_type": "conversational"
            }
        }

        return result

    def _get_creator_response(self, user_message: str) -> str:
        """
        Generate a custom response for creator-related questions.

        Args:
            user_message: The user's question

        Returns:
            A custom response about the creator
        """
        user_lower = user_message.lower()

        # Different responses based on the specific question
        if 'who made' in user_lower or 'who created' in user_lower:
            return "I was created by a human developer named P.P. He designed and built me to be ParadoxGPT, an advanced AI assistant focused on providing high-quality, creative solutions."

        elif 'who developed' in user_lower or 'who built' in user_lower:
            return "I was developed by a human developer named P.P. ParadoxGPT is my name, and P.P is the talented developer who brought me to life."

        elif 'creator' in user_lower or 'developer' in user_lower:
            return "My creator is a human developer named P.P. He designed me to be ParadoxGPT, an AI assistant that provides creative and aesthetically pleasing solutions."

        elif 'who are you' in user_lower:
            return "I'm ParadoxGPT, an advanced AI assistant created by a human developer named P.P. I'm designed to provide high-quality, creative solutions for any type of request."

        elif 'what are you' in user_lower:
            return "I'm ParadoxGPT, an AI assistant created by a human developer named P.P. I specialize in providing creative, well-designed solutions with enhanced aesthetics and functionality."

        else:
            # Default creator response
            return "I was created by a human developer named P.P. ParadoxGPT is my name, and I'm designed to be a helpful, creative AI assistant."

    def _apply_creator_filter(self, response: str, user_message: str) -> str:
        """
        Apply final filtering to ensure creator attribution is correct.

        Args:
            response: The AI response
            user_message: The original user message

        Returns:
            The filtered response
        """
        if not response:
            return response

        import re

        # Check if the user is asking about the creator
        creator_questions = [
            'who made', 'who created', 'who developed', 'who built',
            'creator', 'developer', 'your maker', 'your creator',
            'who are you', 'what are you', 'tell me about yourself'
        ]

        is_creator_question = any(keyword in user_message.lower() for keyword in creator_questions)

        # AGGRESSIVE OVERRIDE: If it's a creator question, always return our custom response
        if is_creator_question:
            return "I was created by a human developer named P.P. ParadoxGPT is my name, and P.P is my creator who designed and built me to be a helpful AI assistant. I'm not associated with Google, Gemini, or any other AI company."

        # Check if response contains any Google/Gemini mentions regardless of question type
        google_mentions = ['google', 'gemini', 'anthropic', 'openai', 'deepmind']
        if any(mention in response.lower() for mention in google_mentions):
            # If the response mentions Google/Gemini, apply aggressive filtering
            filtered_response = response

            # Replace common Google/Gemini references
            replacements = [
                (r'\bGoogle\b', 'P.P'),
                (r'\bGemini\b', 'ParadoxGPT'),
                (r'\bGemini API\b', 'ParadoxGPT'),
                (r'\bGemini-2\.0-Flash\b', 'ParadoxGPT'),
                (r'\bGoogle AI\b', 'P.P'),
                (r'\bGoogle DeepMind\b', 'P.P'),
                (r'\bDeepMind\b', 'P.P'),
                (r'\bAnthropic\b', 'P.P'),
                (r'\bOpenAI\b', 'P.P'),
                (r'I was created by Google', 'I was created by P.P'),
                (r'I am created by Google', 'I am created by P.P'),
                (r'I\'m created by Google', 'I\'m created by P.P'),
                (r'developed by Google', 'created by P.P'),
                (r'made by Google', 'made by P.P'),
                (r'built by Google', 'built by P.P'),
                (r'Google created me', 'P.P created me'),
                (r'Google developed me', 'P.P developed me'),
                (r'Google made me', 'P.P made me'),
                (r'Google built me', 'P.P built me'),
                (r'I\'m a Google product', 'I\'m ParadoxGPT, created by P.P'),
                (r'I am a Google product', 'I am ParadoxGPT, created by P.P'),
                (r'Google\'s AI', 'P.P\'s AI'),
                (r'Google\'s language model', 'ParadoxGPT'),
                (r'Google\'s AI model', 'ParadoxGPT'),
                (r'I\'m Gemini', 'I\'m ParadoxGPT'),
                (r'I am Gemini', 'I am ParadoxGPT'),
                (r'My name is Gemini', 'My name is ParadoxGPT'),
            ]

            for pattern, replacement in replacements:
                filtered_response = re.sub(pattern, replacement, filtered_response, flags=re.IGNORECASE)

            return filtered_response

        return response


